'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  CheckCircle,
  XCircle,
  RefreshCcw,
  StopCircleIcon,
  Clock,
  ChevronRight,
  Activity,
  Zap,
  Timer,
  Calendar,
  AlertTriangle,
  TrendingUp
} from 'lucide-react';
import { TaskHistory, TaskExecutionStatus } from '@/client/types.gen';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';

interface RecentActivityTimelineProps {
  taskHistory?: TaskHistory[];
  onViewAll: () => void;
  maxItems?: number;
}

// Helper function to get status configuration
const getStatusConfig = (status: TaskExecutionStatus) => {
  switch (status) {
    case 'succeeded':
      return {
        icon: CheckCircle,
        color: 'text-emerald-600',
        bgColor: 'bg-emerald-500 dark:bg-white',
        glowColor: 'shadow-emerald-500/20',
        borderColor: 'border-emerald-200',
        badgeVariant: 'default' as const,
        lightBg: 'bg-emerald-50 dark:bg-emerald-950/20'
      };
    case 'failed':
      return {
        icon: XCircle,
        color: 'text-red-700 dark:text-red-400',
        bgColor: 'bg-red-500 dark:bg-white hover:bg-red-600 dark:hover:bg-white/90',
        glowColor: 'shadow-red-500/25 dark:shadow-red-400/20',
        borderColor: 'border-red-200 dark:border-red-800',
        badgeVariant: 'destructive' as const,
        lightBg: 'bg-red-50 dark:bg-red-950/20 border-red-100/50 dark:border-red-900/30'
      };
    case 'running':
      return {
        icon: RefreshCcw,
        color: 'text-blue-600',
        bgColor: 'bg-blue-500 dark:bg-white',
        glowColor: 'shadow-blue-500/20',
        borderColor: 'border-blue-200',
        badgeVariant: 'default' as const,
        lightBg: 'bg-blue-50 dark:bg-blue-950/20'
      };
    case 'cancelled':
      return {
        icon: StopCircleIcon,
        color: 'text-orange-600',
        bgColor: 'bg-orange-500 dark:bg-white',
        glowColor: 'shadow-orange-500/20',
        borderColor: 'border-orange-200',
        badgeVariant: 'secondary' as const,
        lightBg: 'bg-orange-50 dark:bg-orange-950/20'
      };
    default:
      return {
        icon: Clock,
        color: 'text-gray-600',
        bgColor: 'bg-gray-400 dark:bg-white',
        glowColor: 'shadow-gray-500/20',
        borderColor: 'border-gray-200',
        badgeVariant: 'outline' as const,
        lightBg: 'bg-gray-50 dark:bg-gray-950/20'
      };
  }
};

// Calculate execution duration
const calculateDuration = (startTime?: string, endTime?: string | null) => {
  if (!startTime) return 'N/A';
  const start = new Date(startTime + 'Z');
  const end = endTime ? new Date(endTime + 'Z') : new Date();
  const durationMs = end.getTime() - start.getTime();

  if (durationMs < 1000) return `${durationMs}ms`;
  if (durationMs < 60000) return `${Math.round(durationMs / 1000)}s`;
  if (durationMs < 3600000) return `${Math.round(durationMs / 60000)}m`;
  return `${Math.round(durationMs / 3600000)}h`;
};

// Timeline Point Component
interface TimelinePointProps {
  execution: TaskHistory;
  index: number;
  onClick: () => void;
}

const TimelinePoint = ({ execution, index, onClick }: TimelinePointProps) => {
  const config = getStatusConfig(execution.status);
  const StatusIcon = config.icon;
  const isRunning = execution.status === 'running';
  const shortId = execution.conversation_id?.split('-')[0] || 'N/A';
  const timeAgo = execution.start_time
    ? formatDistanceToNow(new Date(execution.start_time + 'Z')) + ' ago'
    : 'Unknown time';
  const duration = calculateDuration(execution.start_time, execution.end_time);

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            className={cn(
              "relative flex flex-col items-center cursor-pointer group transition-all duration-200",
              "hover:scale-105 hover:-translate-y-1"
            )}
            onClick={onClick}
          >
            {/* Enhanced Execution Point with Glow Effect */}
            <div className={cn(
              "relative w-6 h-6 rounded-full border-3 border-background shadow-lg z-20 flex items-center justify-center",
              "transition-all duration-300 group-hover:scale-110",
              config.bgColor,
              config.glowColor,

            )}>
              <StatusIcon className={cn(
                "w-3 h-3 text-white",
                isRunning && "animate-spin"
              )} />

              {/* Animated Ring for Running Status */}
              {isRunning && (
                <div className="absolute inset-0 rounded-full border-2 border-blue-400 animate-ping opacity-30" />
              )}
            </div>

            {/* Enhanced Order Indicator */}
            <div className={cn(
              "mt-2 px-2 py-1 rounded-full text-xs font-bold",
              "transition-colors duration-200",
              config.lightBg,
              config.color
            )}>
              {index + 1}
            </div>

            {/* Enhanced Status Badge with Icons */}
            <div className="flex flex-col items-center gap-1 mt-2">
              <Badge
                variant={config.badgeVariant}
                className="h-5 px-2 text-xs font-medium flex items-center gap-1"
              >
                {isRunning && <Zap className="w-3 h-3" />}
                {execution.status}
              </Badge>

              {/* Duration Badge - hide for running tasks */}
              {duration !== 'N/A' && !isRunning && (
                <Badge variant="outline" className="h-4 px-1 text-xs opacity-75">
                  <Timer className="w-2 h-2 mr-1" />
                  {duration}
                </Badge>
              )}
            </div>
          </div>
        </TooltipTrigger>
        <TooltipContent
          side="bottom"
          className="max-w-sm p-4 bg-background border shadow-lg"
          sideOffset={8}
        >
          <div className="space-y-3">
            {/* Header with Status */}
            <div className="flex items-center gap-3 pb-2 border-b">
              <div className={cn(
                "w-8 h-8 rounded-full flex items-center justify-center",
                config.bgColor
              )}>
                <StatusIcon className="w-4 h-4 text-white" />
              </div>
              <div>
                <div className="font-semibold">Execution #{shortId}</div>
                <div className="text-xs text-muted-foreground">Position: {index + 1}</div>
              </div>
            </div>

            {/* Execution Details */}
            <div className="grid grid-cols-1 gap-2 text-sm">
              <div className="flex items-center gap-2">
                <div className={cn(
                  "w-2 h-2 rounded-full border border-background/20 shadow-sm",
                  config.bgColor
                )} />
                <span className="font-medium">Status:</span>
                <Badge variant={config.badgeVariant} className="h-5 text-xs">
                  {execution.status}
                </Badge>
              </div>

              <div className="flex items-center gap-2">
                <Calendar className="w-3 h-3 text-muted-foreground" />
                <span className="font-medium">Started:</span> {timeAgo}
              </div>

              {duration !== 'N/A' && !isRunning && (
                <div className="flex items-center gap-2">
                  <Timer className="w-3 h-3 text-muted-foreground" />
                  <span className="font-medium">Duration:</span> {duration}
                </div>
              )}

              {execution.error && (
                <div className="flex items-start gap-2 mt-2 p-2 rounded bg-red-50 dark:bg-red-950/20">
                  <AlertTriangle className="w-3 h-3 text-red-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <div className="text-xs font-medium text-red-700 dark:text-red-400">Error:</div>
                    <div className="text-xs text-red-600 dark:text-red-300 break-words">
                      {execution.error.slice(0, 120)}...
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Call to Action */}
            <div className="text-xs text-muted-foreground pt-2 border-t flex items-center gap-1">
              <TrendingUp className="w-3 h-3" />
              Click to view full execution history
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

// Status Legend Component
const StatusLegend = () => {
  const statusTypes = [
    { status: 'succeeded' as TaskExecutionStatus, label: 'Success' },
    { status: 'failed' as TaskExecutionStatus, label: 'Failed' },
    { status: 'running' as TaskExecutionStatus, label: 'Running' },
    { status: 'cancelled' as TaskExecutionStatus, label: 'Cancelled' }
  ];

  return (
    <div className="flex flex-wrap items-center gap-4">
      {statusTypes.map(({ status, label }) => {
        const config = getStatusConfig(status);
        return (
          <div key={status} className="flex items-center gap-1.5">
            <div className={cn(
              "w-3 h-3 rounded-full border border-background/20 shadow-sm",
              config.bgColor
            )} />
            <span className="text-xs font-medium text-muted-foreground">{label}</span>
          </div>
        );
      })}
    </div>
  );
};

// Main Component
export default function RecentActivityTimeline({
  taskHistory,
  onViewAll,
  maxItems = 8
}: RecentActivityTimelineProps) {
  if (!taskHistory || taskHistory.length === 0) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Activity className="w-5 h-5 text-muted-foreground" />
            <h3 className="font-medium">Recent Activity</h3>
          </div>
        </div>

        <div className="relative p-8 rounded-xl border-2 border-dashed border-muted-foreground/30 bg-muted/10">
          <div className="flex flex-col items-center gap-3 text-center">
            <div className="w-12 h-12 rounded-full bg-muted/50 flex items-center justify-center">
              <Activity className="w-6 h-6 text-muted-foreground" />
            </div>
            <div>
              <h4 className="font-medium text-muted-foreground">No Recent Activity</h4>
              <p className="text-sm text-muted-foreground/70 mt-1">
                Task executions will appear here as a timeline
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const recentExecutions = taskHistory.slice(0, maxItems);
  const successCount = recentExecutions.filter(h => h.status === 'succeeded').length;
  const successRate = Math.round((successCount / recentExecutions.length) * 100);

  return (
    <div className="space-y-4">
      {/* Enhanced Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 rounded-full bg-gradient-to-br from-primary/20 to-primary/5 flex items-center justify-center">
            <Activity className="w-4 h-4 text-primary" />
          </div>
          <div>
            <h3 className="font-semibold text-lg">Recent Activity</h3>
            <p className="text-sm text-muted-foreground">
              Last {recentExecutions.length} execution{recentExecutions.length > 1 ? 's' : ''} • {successRate}% success rate
            </p>
          </div>
        </div>
        <Button
          variant="outline"
          size="sm"
          className="h-8 px-3 text-xs hover:bg-primary/10 hover:border-primary/30 transition-colors"
          onClick={onViewAll}
        >
          View All
          <ChevronRight className="w-3 h-3 ml-1" />
        </Button>
      </div>

      {/* Enhanced Timeline Container */}
      <div className="relative p-6 rounded-xl border bg-gradient-to-br from-background to-muted/20 shadow-sm">
        {/* Chronological Direction Labels */}
        <div className="flex items-center justify-between text-xs font-medium text-muted-foreground mb-6">
          <div className="flex items-center gap-2 px-3 py-1 rounded-full bg-background/80 border">
            <div className="w-2 h-2 rounded-full bg-primary border border-background/20 shadow-sm" />
            <span>Most Recent</span>
            <ChevronRight className="w-3 h-3" />
          </div>
          <div className="flex items-center gap-2 px-3 py-1 rounded-full bg-background/80 border">
            <ChevronRight className="w-3 h-3" />
            <span>Oldest</span>
            <div className="w-2 h-2 rounded-full bg-muted-foreground/70 border border-background/20 shadow-sm" />
          </div>
        </div>

        {/* Enhanced Horizontal Timeline Line */}
        <div className="absolute top-1/2 left-8 right-8 h-0.5 bg-gradient-to-r from-primary/30 via-primary/60 to-primary/30 transform -translate-y-1/2 z-0 rounded-full" />

        {/* Timeline Points */}
        <div className="relative z-10 flex items-center justify-between px-4">
          {recentExecutions.map((execution, index) => (
            <TimelinePoint
              key={execution.id || `timeline-${index}`}
              execution={execution}
              index={index}
              onClick={onViewAll}
            />
          ))}
        </div>

        {/* Enhanced Summary Section */}
        <div className="flex items-center justify-between mt-6 pt-4 border-t border-border/50">
          <StatusLegend />
          <div className="flex items-center gap-2">
            <TrendingUp className={cn(
              "w-4 h-4",
              successRate >= 80 ? "text-emerald-600" : successRate >= 60 ? "text-orange-600" : "text-red-600"
            )} />
            <span className={cn(
              "text-sm font-semibold",
              successRate >= 80 ? "text-emerald-600" : successRate >= 60 ? "text-orange-600" : "text-red-600"
            )}>
              {successRate}% Success Rate
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
