'use client';

import { <PERSON><PERSON><PERSON>, Cloud, DollarSign, Shield } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui/tooltip';

interface ExamplePrompt {
  icon: React.ReactNode;
  title: string;
  question: string;
  color: string;
  id: string;
}

const EXAMPLE_PROMPTS: ExamplePrompt[] = [
  {
    icon: <DollarSign className="h-8 w-8 text-emerald-500" />,
    title: 'Cost Optimization',
    question:
      'Can you identify any underutilized EC2 instances in our environment?',
    color: 'from-emerald-400 to-teal-600',
    id: 'ec2-optimization'
  },
  {
    icon: <Shield className="h-8 w-8 text-blue-500" />,
    title: 'Security Audit',
    question:
      'Can you review our S3 buckets and identify any potential security risks?',
    color: 'from-blue-400 to-indigo-600',
    id: 's3-security'
  },
  {
    icon: <BarChart className="h-8 w-8 text-violet-500" />,
    title: 'Performance Monitoring',
    question: 'Is our main web application performing normally on our ALB?',
    color: 'from-violet-400 to-purple-600',
    id: 'alb-monitoring'
  },
  {
    icon: <Cloud className="h-8 w-8 text-amber-500" />,
    title: 'Resource Provisioning',
    question:
      'How can I set up a new VPC with maximum cost savings for a development environment?',
    color: 'from-amber-400 to-orange-600',
    id: 'vpc-setup'
  }
];

const EXAMPLE_PROMPT_DETAILS: Record<
  string,
  { description: string; category?: string }
> = {
  'ec2-optimization': {
    description:
      'Analyze EC2 instance utilization metrics to identify opportunities for cost optimization and resource right-sizing.',
    category: 'optimization'
  },
  's3-security': {
    description:
      'Perform a comprehensive security audit of S3 bucket configurations, permissions, and access patterns.',
    category: 'security'
  },
  'alb-monitoring': {
    description:
      'Monitor and analyze the performance metrics of your web application load balancer for any anomalies or issues.',
    category: 'monitoring'
  },
  'vpc-setup': {
    description:
      'Design and implement a cost-effective VPC architecture optimized for development workloads.',
    category: 'optimization'
  }
};

interface ExamplePromptsProps {
  onPromptClick: (prompt: ExamplePrompt) => void;
}

export function ExamplePrompts({ onPromptClick }: ExamplePromptsProps) {
  return (
    <div className="mx-auto mt-4 h-full w-full max-w-4xl px-4">
      <h2 className="mb-2 text-center text-xl font-semibold text-foreground/90 md:mb-3 md:text-2xl">
        Quick Start
      </h2>
      <div className="flex animate-fade-in flex-row items-center justify-center gap-4 py-1 md:gap-6">
        <TooltipProvider>
          {EXAMPLE_PROMPTS.map((prompt, idx) => (
            <Tooltip key={prompt.id}>
              <TooltipTrigger asChild>
                <button
                  onClick={() => onPromptClick(prompt)}
                  className={cn(
                    'group relative rounded-full border p-3 transition-all duration-300 md:p-4',
                    'hover:scale-110 hover:border-primary/60 hover:shadow-md',
                    'bg-card/50 backdrop-blur-sm'
                  )}
                  style={{ animationDelay: `${idx * 100}ms` }}
                >
                  {/* Gradient background that appears on hover */}
                  <div
                    className={cn(
                      'absolute inset-0 rounded-full opacity-0 transition-opacity duration-300 group-hover:opacity-10',
                      `bg-gradient-to-br ${prompt.color}`
                    )}
                  />
                  {prompt.icon}
                </button>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-sm">{prompt.question}</p>
              </TooltipContent>
            </Tooltip>
          ))}
        </TooltipProvider>
      </div>
    </div>
  );
}

export type { ExamplePrompt };
export { EXAMPLE_PROMPT_DETAILS };
