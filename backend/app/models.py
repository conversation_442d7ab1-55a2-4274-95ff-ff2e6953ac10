import uuid
from datetime import UTC, datetime
from enum import Enum, StrEnum
from typing import Literal, Optional
from uuid import UUI<PERSON>, uuid4

from pydantic import UUID4, BaseModel, EmailStr, field_validator
from sqlalchemy.dialects.postgresql import ARRAY as PostgreSQLArray
from sqlalchemy.dialects.postgresql import UUID as sa_UUID
from sqlmodel import (
    JSON,
    Column,
    DateTime,
    Field,
    Relationship,
    SQLModel,
    String,
    Text,
    UniqueConstraint,
    func,
)

from app.core.constants import (
    MAX_DESCRIPTION_LENGTH,
    MAX_TITLE_LENGTH,
)


# Shared properties
class UserBase(SQLModel):
    email: EmailStr = Field(unique=True, index=True, max_length=255)
    is_active: bool = Field(default=False)
    is_email_verified: bool = Field(default=False)
    is_superuser: bool = False
    last_login_time: datetime | None = None
    full_name: str | None = Field(default=None, max_length=255)
    avatar_url: str | None = Field(default=None, max_length=1024)


# Properties to receive via API on creation
class UserCreate(UserBase):
    password: str = Field(min_length=8, max_length=40)


class UserRegister(SQLModel):
    email: EmailStr = Field(max_length=255)
    password: str = Field(min_length=8, max_length=40)
    full_name: str | None = Field(default=None, max_length=255)


# Properties to receive via API on update, all are optional
class UserUpdate(UserBase):
    email: EmailStr | None = Field(default=None, max_length=255)  # type: ignore
    password: str | None = Field(default=None, min_length=8, max_length=40)


class UserUpdateMe(SQLModel):
    full_name: str | None = Field(default=None, max_length=255)
    email: EmailStr | None = Field(default=None, max_length=255)
    avatar_url: str | None = Field(default=None, max_length=1024)


class UpdatePassword(SQLModel):
    current_password: str = Field(min_length=8, max_length=40)
    new_password: str = Field(min_length=8, max_length=40)


# Database model, database table inferred from class name
class User(UserBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    hashed_password: str
    workspaces: list["UserWorkspace"] = Relationship(
        back_populates="user", sa_relationship_kwargs={"cascade": "all, delete-orphan"}
    )
    activation_tokens: list["UserActivationToken"] = Relationship(
        back_populates="user", sa_relationship_kwargs={"cascade": "all, delete-orphan"}
    )

    owned_tasks: list["Task"] = Relationship(
        back_populates="owner",
        sa_relationship_kwargs={"foreign_keys": "[Task.owner_id]"},
    )

    own_workspaces: list["Workspace"] = Relationship(
        back_populates="owner", sa_relationship_kwargs={"cascade": "all, delete-orphan"}
    )

    usage_quota: "UsageQuota" = Relationship(
        back_populates="user", sa_relationship_kwargs={"cascade": "all, delete-orphan"}
    )

    notifications: list["Notification"] = Relationship(
        back_populates="user", sa_relationship_kwargs={"cascade": "all, delete-orphan"}
    )

    # Knowledge Base relationship
    knowledge_bases: list["KB"] = Relationship(
        back_populates="owner",
        sa_relationship_kwargs={
            "foreign_keys": "[KB.owner_id]",
            "cascade": "all, delete-orphan",
        },
    )


# Properties to return via API, id is always required


class UserPublic(UserBase):
    id: uuid.UUID


class AuthorizedUser(UserPublic):
    current_workspace_id: uuid.UUID | None = None
    workspaces: list["Workspace"] | None = None
    own_workspaces: list["Workspace"] | None = None


class UserDetail(UserPublic):
    workspaces: list["Workspace"] | None = None
    own_workspaces: list["Workspace"] | None = None
    items: list["Item"] | None = None


class UsersPublic(SQLModel):
    data: list[UserPublic]
    count: int


# Shared properties
class ItemBase(SQLModel):
    title: str = Field(min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=255)


# Properties to receive on item creation
class ItemCreate(ItemBase):
    pass


# Properties to receive on item update
class ItemUpdate(ItemBase):
    title: str | None = Field(default=None, min_length=1, max_length=255)  # type: ignore


# Database model, database table inferred from class name
class Item(ItemBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    title: str = Field(max_length=255)


# Properties to return via API, id is always required
class ItemPublic(ItemBase):
    id: uuid.UUID
    owner_id: uuid.UUID


class ItemsPublic(SQLModel):
    data: list[ItemPublic]
    count: int


# Generic message
# class Message(SQLModel):
#     message: str


# JSON payload containing access token
class Token(SQLModel):
    access_token: str
    token_type: str = "bearer"
    workspace_id: uuid.UUID | None = None
    is_first_login: bool = False
    slack_oauth: bool = False
    app_id: str | None = None
    team_id: str | None = None


# Contents of JWT token
class TokenPayload(SQLModel):
    sub: str | None = None
    workspace_id: uuid.UUID | None = None


class NewPassword(SQLModel):
    token: str
    new_password: str = Field(min_length=8, max_length=40)


### AWS data models ###
# Assume we have a function to get the encryption key


class CredentialStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    EXPIRED = "expired"


class AWSAccountCredentials(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    aws_account_id: uuid.UUID = Field(foreign_key="awsaccount.id", ondelete="CASCADE")
    aws_account: "AWSAccount" = Relationship(back_populates="credential")
    access_key_id: str = Field(max_length=128)
    secret_access_key: str = Field(max_length=256)
    status: CredentialStatus = Field(default=CredentialStatus.ACTIVE)
    created_at: datetime = Field(default_factory=datetime.now)
    expires_at: datetime | None = Field(default=None)
    last_used_at: datetime | None = Field(default=None)
    last_rotated_at: datetime | None = Field(default=None)

    __table_args__ = (
        UniqueConstraint("aws_account_id", name="uq_aws_account_credential"),
    )


class AccountEnvironement(str, Enum):
    PRODUCTION = "production"
    STAGING = "staging"
    DEVELOPMENT = "development"


class AWSAccountBase(SQLModel):
    name: str = Field(min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    environment: AccountEnvironement = Field(nullable=False)


class AWSAccountCreate(AWSAccountBase):
    account_id: str = Field(min_length=12, max_length=12)
    access_key_id: str = Field(max_length=128)
    secret_access_key: str = Field(max_length=256)
    workspace_id: uuid.UUID
    regions: list[str] = Field(default=[], min_items=1)
    types: list[str] = Field(default=[], min_items=1)
    cron_pattern: str = Field(min_length=1, max_length=20)


class AWSAccountUpdate(AWSAccountBase):
    name: str | None = Field(default=None, min_length=1, max_length=255)
    account_id: str | None = Field(default=None)
    environment: str | None = Field(default=None, max_length=50)
    regions: list[str] | None = []
    types: list[str] | None = []
    cron_pattern: str | None = None
    access_key_id: str | None = Field(default=None, max_length=128)
    secret_access_key: str | None = Field(default=None, max_length=256)

    @field_validator("account_id")
    def validate_account_id(cls, v):
        if v is not None and v != "" and len(v) != 12:
            raise ValueError(
                "account_id must be exactly 12 characters long when provided"
            )
        return v


class AWSAccount(AWSAccountBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    account_id: str = Field(min_length=12, max_length=12)
    credential: AWSAccountCredentials | None = Relationship(
        back_populates="aws_account",
        sa_relationship_kwargs={"uselist": False, "cascade": "all, delete-orphan"},
    )
    workspace_id: uuid.UUID = Field(foreign_key="workspace.id", ondelete="CASCADE")
    workspace: "Workspace" = Relationship(back_populates="aws_account")
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    is_active: bool = Field(default=True)


class AWSAccountPublic(AWSAccountBase):
    workspace_id: uuid.UUID
    id: uuid.UUID


class AWSAccountDetail(AWSAccountPublic):
    access_key_id: str
    secret_access_key: str
    account_id: str


class WorkspaceBase(SQLModel):
    name: str = Field(min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=1000)


class WorkspaceCreate(WorkspaceBase):
    owner_id: uuid.UUID | None = None


class WorkspaceUpdate(WorkspaceBase):
    pass


class WorkspacePublic(WorkspaceBase):
    id: uuid.UUID
    is_default: bool
    is_deleted: bool = Field(default=False)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)


class WorkspaceDetail(WorkspacePublic):
    aws_account: AWSAccountDetail | None = Field(default=None)
    settings: Optional["WorkspaceSetting"]
    provider_settings: "Setting"


class WorkspacesPublic(SQLModel):
    data: list[WorkspacePublic]
    count: int


class WorkspaceSetting(SQLModel, table=True):
    """Settings for a workspace"""

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    workspace_id: uuid.UUID = Field(
        foreign_key="workspace.id",
        unique=True,
        ondelete="CASCADE",
    )
    regions: list[str] = Field(default=[], sa_column=Column(PostgreSQLArray(String)))
    types: list[str] = Field(default=[], sa_column=Column(PostgreSQLArray(String)))
    cron_pattern: str = Field(max_length=250)

    workspace: "Workspace" = Relationship(back_populates="settings")


class Workspace(WorkspaceBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    aws_account: "AWSAccount" = Relationship(
        back_populates="workspace",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    owner_id: uuid.UUID = Field(
        foreign_key="user.id", nullable=False, ondelete="CASCADE"
    )
    owner: User = Relationship(back_populates="own_workspaces")
    agents: list["Agent"] = Relationship(
        back_populates="workspace",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    tasks: list["Task"] = Relationship(
        back_populates="workspace",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    users: list["UserWorkspace"] = Relationship(
        back_populates="workspace",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    workflows: list["Workflow"] = Relationship(
        back_populates="workspace",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    resources: list["Resource"] = Relationship(
        back_populates="workspace",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    mcp_config: "MCPConfig" = Relationship(
        back_populates="workspace",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    settings: "WorkspaceSetting" = Relationship(
        back_populates="workspace",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    built_in_connectors: list["WorkspaceBuiltInConnector"] = Relationship(
        back_populates="workspace",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    knowledge_bases: list["KB"] = Relationship(
        back_populates="workspace",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    is_default: bool = Field(default=False)
    is_deleted: bool = Field(default=False)

    def delete(self):
        self.is_deleted = True
        self.updated_at = datetime.now()

    class Config:
        from_attributes = True
        arbitrary_types_allowed = True


class Connector(SQLModel):
    id: UUID = Field(default_factory=uuid4, primary_key=True)
    name: str = Field(
        unique=True, index=True, description="Unique identifier for the connector"
    )
    display_name: str = Field(description="Human-readable name for the connector")
    description: str | None = Field(default=None, sa_column=Column(Text))
    default_required_permission: bool = Field(default=False)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)


class BuiltInConnector(Connector, table=True):
    """Definition of built-in connectors available in the system"""

    workspaces_builtin_connectors: list["WorkspaceBuiltInConnector"] = Relationship(
        back_populates="connector",
        sa_relationship_kwargs={
            "primaryjoin": "WorkspaceBuiltInConnector.connector_id == BuiltInConnector.id"
        },
    )


class AgentConnectorBuiltInConnector(SQLModel, table=True):
    """Link table for AgentConnector and WorkspaceBuiltInConnector many-to-many relationship"""

    agent_connector_id: UUID = Field(
        foreign_key="agentconnector.id", primary_key=True, ondelete="CASCADE"
    )
    workspace_builtin_connector_id: UUID = Field(
        foreign_key="workspacebuiltinconnector.id", primary_key=True, ondelete="CASCADE"
    )


class WorkspaceBuiltInConnector(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    workspace_id: uuid.UUID = Field(foreign_key="workspace.id", ondelete="CASCADE")
    connector_id: uuid.UUID = Field(
        foreign_key="builtinconnector.id", ondelete="CASCADE"
    )
    workspace: "Workspace" = Relationship(back_populates="built_in_connectors")
    connector: "BuiltInConnector" = Relationship(
        back_populates="workspaces_builtin_connectors"
    )
    agent_connectors: list["AgentConnector"] = Relationship(
        back_populates="builtin_connectors",
        link_model=AgentConnectorBuiltInConnector,
        sa_relationship_kwargs={"lazy": "selectin"},
    )
    is_active: bool = Field(default=True)
    required_permission: bool = Field(default=False)


class ResourceStatus(str, Enum):
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    FOUND = "found"
    DELETED = "deleted"


class ResourceBase(SQLModel):
    name: str = Field(min_length=1, max_length=255)
    arn: str = Field(max_length=2048)
    tags: dict = Field(default={}, sa_column=Column(JSON))
    configurations: dict = Field(default={}, sa_column=Column(JSON))
    description: str | None = Field(default=None, max_length=1000)
    type: str = Field(max_length=50)
    region: str = Field(max_length=50)
    status: ResourceStatus = Field(default=ResourceStatus.FOUND)


class ResourceCreate(ResourceBase):
    workspace_id: uuid.UUID
    type: str
    region: str


class ResourceUpdate(ResourceBase):
    name: str | None = Field(default=None, min_length=1, max_length=255)
    arn: str | None = Field(default=None, max_length=2048)


class Resource(ResourceBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    metrics: list["Metric"] = Relationship(
        back_populates="resource",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    recommendations: list["Recommendation"] = Relationship(
        back_populates="resource",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    workspace_id: uuid.UUID = Field(foreign_key="workspace.id", ondelete="CASCADE")
    workspace: "Workspace" = Relationship(back_populates="resources")
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    is_active: bool = Field(default=True)
    conversations: list["Conversation"] = Relationship(
        back_populates="resource",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )

    @property
    def total_recommendation(self) -> int:
        return len(
            [
                recommendation
                for recommendation in self.recommendations
                if recommendation.status == RecommendationStatus.PENDING
            ]
        )

    @property
    def total_potential_saving(self) -> float:
        return round(
            sum(
                [
                    recommendation.potential_savings
                    for recommendation in self.recommendations
                    if recommendation.status == RecommendationStatus.PENDING
                ]
            ),
            2,
        )

    def get_resource_context(self) -> str:
        return f"""
        Resource Name: {self.name}
        Resource ARN: {self.arn}
        Resource Type: {self.type}
        Resource Region: {self.region}
        Resource Description: {self.description}
        Resource Status: {self.status}
        Resource Tags: {self.tags}
        Resource Configurations: {self.configurations}
        """


class ResourcePublic(ResourceBase):
    id: uuid.UUID
    workspace: WorkspacePublic
    type: str
    region: str
    total_recommendation: int
    total_potential_saving: float
    updated_at: datetime


class UserWorkspace(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    user_id: uuid.UUID = Field(foreign_key="user.id", ondelete="CASCADE")
    user: User = Relationship(back_populates="workspaces")
    workspace_id: uuid.UUID = Field(foreign_key="workspace.id", ondelete="CASCADE")
    workspace: Workspace = Relationship(back_populates="users")
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

    __table_args__ = (
        UniqueConstraint("user_id", "workspace_id", name="uq_user_workspace"),
    )


class MetricType(str, Enum):
    USAGE = "usage"
    PERFORMANCE = "performance"
    COST = "cost"


class MetricBase(SQLModel):
    name: str = Field(max_length=100)
    value: float
    unit: str = Field(max_length=50)
    timestamp: datetime
    type: MetricType


class MetricCreate(MetricBase):
    resource_id: uuid.UUID


class MetricUpdate(MetricBase):
    name: str | None = Field(default=None, max_length=100)
    value: float | None = None
    unit: str | None = Field(default=None, max_length=50)
    timestamp: datetime | None = None
    type: MetricType | None = None


class Metric(MetricBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    resource_id: uuid.UUID = Field(foreign_key="resource.id", ondelete="CASCADE")
    resource: Resource = Relationship(back_populates="metrics")
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)


class MetricPublic(MetricBase):
    id: uuid.UUID
    resource_id: uuid.UUID


class RecommendationType(str, Enum):
    # Resource Optimization
    INSTANCE_RIGHTSIZING = "instance_rightsizing"
    AUTOSCALING_OPTIMIZATION = "autoscaling_optimization"
    AUTO_START_STOP_OPTIMIZATION = "auto_start_stop_optimization"
    VOLUME_OPTIMIZATION = "volume_optimization"
    SNAPSHOT_CLEANUP = "snapshot_cleanup"

    # Pricing Models
    RESERVED_INSTANCE_RECOMMENDATION = "reserved_instance_recommendation"
    SAVINGS_PLAN_RECOMMENDATION = "savings_plan_recommendation"
    SPOT_INSTANCE_USAGE = "spot_instance_usage"

    # Unused and Underutilized Resources
    IDLE_RESOURCE_CLEANUP = "idle_resource_cleanup"
    UNUSED_EIP_CLEANUP = "unused_eip_cleanup"
    ORPHANED_SNAPSHOT_CLEANUP = "orphaned_snapshot_cleanup"
    UNDERUTILIZED_EBS_CLEANUP = "underutilized_ebs_cleanup"

    # Architectural Improvements
    SERVERLESS_MIGRATION = "serverless_migration"
    CONTAINER_ADOPTION = "container_adoption"
    MULTI_AZ_OPTIMIZATION = "multi_az_optimization"

    # Data Transfer and Networking
    DATA_TRANSFER_OPTIMIZATION = "data_transfer_optimization"
    CLOUDFRONT_OPTIMIZATION = "cloudfront_optimization"
    NAT_GATEWAY_OPTIMIZATION = "nat_gateway_optimization"

    # Service-Specific Optimizations
    RDS_OPTIMIZATION = "rds_optimization"
    REDSHIFT_OPTIMIZATION = "redshift_optimization"
    DYNAMODB_OPTIMIZATION = "dynamodb_optimization"
    S3_STORAGE_CLASS_OPTIMIZATION = "s3_storage_class_optimization"
    LAMBDA_OPTIMIZATION = "lambda_optimization"

    # Cost Allocation and Tagging
    TAGGING_IMPROVEMENT = "tagging_improvement"
    COST_ALLOCATION_IMPROVEMENT = "cost_allocation_improvement"

    # Operational Excellence
    COST_ANOMALY_DETECTION = "cost_anomaly_detection"
    BUDGET_ALERT_SETUP = "budget_alert_setup"
    COST_EXPLORER_USAGE = "cost_explorer_usage"

    # Modernization
    MODERNIZE_LEGACY_SERVICES = "modernize_legacy_services"
    MIGRATE_TO_GRAVITON = "migrate_to_graviton"

    # Compliance and Governance
    COMPLIANCE_OPTIMIZATION = "compliance_optimization"
    GOVERNANCE_IMPROVEMENT = "governance_improvement"

    # Cross-cutting Concerns
    CROSS_REGION_OPTIMIZATION = "cross_region_optimization"
    CROSS_ACCOUNT_OPTIMIZATION = "cross_account_optimization"

    # Advanced Strategies
    PREDICTIVE_SCALING = "predictive_scaling"
    AI_DRIVEN_OPTIMIZATION = "ai_driven_optimization"
    QUANTUM_COMPUTING_READINESS = "quantum_computing_readiness"

    # Sustainability
    CARBON_FOOTPRINT_REDUCTION = "carbon_footprint_reduction"
    RENEWABLE_ENERGY_USAGE = "renewable_energy_usage"

    # Marketplace and Third-party Solutions
    MARKETPLACE_ALTERNATIVE = "marketplace_alternative"
    THIRD_PARTY_TOOL_RECOMMENDATION = "third_party_tool_recommendation"

    # Custom and Miscellaneous
    CUSTOM_OPTIMIZATION = "custom_optimization"
    OTHER = "other"

    # New EC2-specific types
    EC2_FLEET_OPTIMIZATION = "ec2_fleet_optimization"
    SPOT_FLEET_OPTIMIZATION = "spot_fleet_optimization"
    GRAVITON_MIGRATION = "graviton_migration"
    PREDICTIVE_SCALING_OPTIMIZATION = "predictive_scaling_optimization"
    INSTANCE_CONNECT_ENDPOINT = "instance_connect_endpoint"


class RecommendationStatus(str, Enum):
    PENDING = "pending"
    IMPLEMENTED = "implemented"
    IGNORED = "ignored"
    IN_PROGRESS = "in_progress"


class RecommendationBase(SQLModel):
    type: RecommendationType
    title: str = Field(max_length=255)
    description: str = Field(sa_column=Column(Text))
    potential_savings: float
    effort: str = Field(max_length=50)  # e.g., low, medium, high
    risk: str = Field(max_length=50)  # e.g., low, medium, high
    status: RecommendationStatus = Field(default=RecommendationStatus.PENDING)


class RecommendationCreate(RecommendationBase):
    resource_id: uuid.UUID


class RecommendationUpdate(RecommendationBase):
    type: RecommendationType | None = None
    title: str | None = Field(default=None, max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    potential_savings: float | None = None
    effort: str | None = Field(default=None, max_length=50)
    risk: str | None = Field(default=None, max_length=50)
    status: RecommendationStatus | None = None


class Recommendation(RecommendationBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    resource_id: uuid.UUID = Field(foreign_key="resource.id", ondelete="CASCADE")
    resource: Resource = Relationship(back_populates="recommendations")
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    implemented_at: datetime | None = Field(default=None)
    implemented_by: uuid.UUID | None = Field(default=None, foreign_key="user.id")


class RecommendationPublic(RecommendationBase):
    id: uuid.UUID
    resource_id: uuid.UUID
    resource: ResourcePublic


class RecommendationOveralPublic(SQLModel):
    total_resource_scanned: int
    total_well_optimized: int
    total_optimization_opportunities: int
    total_estimated_saving_amount: float


# Public list responses
class AWSAccountsPublic(SQLModel):
    data: list[AWSAccountPublic]
    count: int


class ResourcesPublic(SQLModel):
    data: list[ResourcePublic]
    count: int


class MetricsPublic(SQLModel):
    data: list[MetricPublic]
    count: int


class RecommendationsPublic(SQLModel):
    data: list[RecommendationPublic]
    count: int


class WorkflowStatus(str, Enum):
    CREATED = "created"
    UNVALIDATED = "unvalidated"
    RUNNING = "running"
    PENDING = "pending"
    COMPLETED = "completed"
    ERROR = "error"


class WorkflowBase(SQLModel):
    name: str = Field(max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    workspace_id: uuid.UUID


class Workflow(WorkflowBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    workspace_id: uuid.UUID = Field(foreign_key="workspace.id", ondelete="CASCADE")
    workspace: Workspace = Relationship(back_populates="workflows")
    nodes: list["WorkflowNode"] = Relationship(back_populates="workflow")
    status: WorkflowStatus = Field(default=WorkflowStatus.CREATED)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)


class WorkflowCreate(WorkflowBase):
    pass


class WorkflowUpdate(SQLModel):
    name: str | None = None
    description: str | None = None
    workspace_id: uuid.UUID | None = None


class WorkflowPublic(WorkflowBase):
    id: uuid.UUID
    nodes: list["WorkflowNodePublic"]
    status: WorkflowStatus = Field(default=WorkflowStatus.CREATED)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)


class WorkflowsPublic(SQLModel):
    data: list[WorkflowPublic]
    count: int


class NodeType(str, Enum):
    START = "start"
    TOOL = "tool"
    HUMAN_IN_LOOP = "human_in_loop"
    END = "end"
    OUTPUT = "output"


class WorkflowNodeBase(SQLModel):
    type: NodeType
    name: str = Field(max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    position: int
    data: dict = Field(default={}, sa_column=Column(JSON))


class WorkflowNode(WorkflowNodeBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    workflow_id: uuid.UUID = Field(foreign_key="workflow.id")
    parent_id: uuid.UUID | None = Field(default=None, foreign_key="workflownode.id")
    workflow: "Workflow" = Relationship(back_populates="nodes")
    parent: Optional["WorkflowNode"] = Relationship(
        back_populates="children",
        sa_relationship_kwargs={"remote_side": "WorkflowNode.id"},
    )
    children: list["WorkflowNode"] = Relationship(back_populates="parent")
    status: WorkflowStatus = Field(default=WorkflowStatus.CREATED)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)


class WorkflowNodeCreate(WorkflowNodeBase):
    workflow_id: uuid.UUID
    parent_id: uuid.UUID | None = None


class WorkflowNodeUpdate(SQLModel):
    name: str | None = None
    description: str | None = None
    position: int | None = None


class WorkflowNodePublic(WorkflowNodeBase):
    id: uuid.UUID
    status: WorkflowStatus


### Agent ###


class AgentConnector(SQLModel, table=True):
    id: UUID = Field(default_factory=uuid4, primary_key=True)
    agent_id: UUID = Field(foreign_key="agent.id", ondelete="CASCADE")
    builtin_connectors: list["WorkspaceBuiltInConnector"] = Relationship(
        back_populates="agent_connectors",
        link_model=AgentConnectorBuiltInConnector,
        sa_relationship_kwargs={"lazy": "selectin"},
    )
    mcp_servers: list[str] = Field(sa_column=Column(PostgreSQLArray(String)))
    agent: "Agent" = Relationship(back_populates="connectors")


class AgentType(StrEnum):
    """Defines the supported types of agents in the system."""

    CONVERSATION_AGENT = "conversation_agent"
    AUTONOMOUS_AGENT = "autonomous_agent"


class AgentBase(SQLModel):
    """Base model for Agent containing common attributes."""

    title: str = Field(
        min_length=1,
        max_length=255,
        index=True,
        description="The title/name of the agent",
    )
    description: str | None = Field(
        default=None,
        sa_column=Column(Text),
        description="Detailed description of the agent's purpose",
    )
    type: AgentType = Field(
        default=AgentType.CONVERSATION_AGENT,
        index=True,
        description="Type of the agent",
    )
    instructions: str | None = Field(
        default=None,
        sa_column=Column(Text),
        description="Custom instructions for the agent",
    )


class Agent(AgentBase, table=True):
    """Agent model with database configuration."""

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True, index=True)
    created_at: datetime = Field(default_factory=datetime.now, nullable=False)
    updated_at: datetime = Field(default_factory=datetime.now, nullable=False)
    is_deleted: bool = Field(default=False, index=True)
    is_active: bool = Field(default=True, index=True)

    # Relationships
    workspace_id: uuid.UUID = Field(
        foreign_key="workspace.id", index=True, ondelete="CASCADE"
    )
    workspace: "Workspace" = Relationship(
        back_populates="agents",
        sa_relationship_kwargs={"lazy": "joined", "cascade": "all"},
    )
    conversations: list["Conversation"] = Relationship(
        back_populates="agent",
        sa_relationship_kwargs={"cascade": "all, delete-orphan", "lazy": "selectin"},
    )

    connectors: list["AgentConnector"] = Relationship(
        back_populates="agent",
        sa_relationship_kwargs={"lazy": "selectin", "cascade": "all, delete-orphan"},
    )

    agent_contexts: "AgentContext" = Relationship(
        back_populates="agent",
        sa_relationship_kwargs={"lazy": "selectin", "cascade": "all, delete-orphan"},
    )


# Properties to return via API, id is always required


class CloudProvider(str, Enum):
    AWS: str = "AWS"


class Setting(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    provider_name: CloudProvider = Field(default=CloudProvider.AWS)
    regions: list[str] = Field(default=[], sa_column=Column(PostgreSQLArray(String)))
    types: list[str] = Field(default=[], sa_column=Column(PostgreSQLArray(String)))
    cron_patterns: list[str] = Field(
        default=[], sa_column=Column(PostgreSQLArray(String))
    )


class ConversationBase(SQLModel):
    model_provider: str = Field(max_length=255)


class ConversationCreate(ConversationBase):
    agent_id: uuid.UUID


class SharedConversationStatus(str, Enum):
    ACTIVE = "active"
    EXPIRED = "expired"
    REVOKED = "revoked"


class Conversation(ConversationBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    name: str = Field(default="Untitled")
    agent_id: uuid.UUID = Field(foreign_key="agent.id", ondelete="CASCADE")
    agent: Agent = Relationship(back_populates="conversations")
    messages: list["Message"] = Relationship(
        back_populates="conversation",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

    resource_id: uuid.UUID | None = Field(default=None, foreign_key="resource.id")
    resource: Resource | None = Relationship(
        back_populates="conversations",
        sa_relationship_kwargs={"lazy": "selectin"},
    )
    instructions: str | None = Field(default=None, sa_column=Column(Text))
    is_deleted: bool = Field(default=False)

    # Sharing fields
    share_id: uuid.UUID | None = Field(default=None, unique=True, index=True)
    is_shared: bool = Field(default=False)
    shared_at: datetime | None = Field(default=None)
    shared_by: uuid.UUID | None = Field(default=None, foreign_key="user.id")

    __table_args__ = (UniqueConstraint("share_id", name="uq_conversation_share_id"),)


class ConversationPublic(SQLModel):
    id: uuid.UUID
    agent_id: uuid.UUID
    name: str
    model_provider: str
    created_at: datetime

    class Config:
        from_attributes = True


class ConversationsPublic(SQLModel):
    """Response model for paginated conversations list.

    Attributes:
        data: List of conversation items
        count: Total number of items available (before pagination)
    """

    data: list[ConversationPublic]
    count: int


### Message models ###


class MessageActionType(str, Enum):
    """
    Enum for the type of action that can be taken by the message
    """

    NONE = "none"
    RECOMMENDATION = "recommendation"


class MessageDisplayComponentType(str, Enum):
    """
    Enum for display component types (currently supporting only tables and charts)
    """

    TABLE = "table"
    CHART = "chart"


class ChartType(str, Enum):
    """
    Enum for different types of charts available in the system
    """

    LINE = "line"
    BAR = "bar"
    PIE = "pie"
    DOUGHNUT = "doughnut"
    AREA = "area"
    SCATTER = "scatter"
    RADAR = "radar"
    STEP_AREA = "step_area"


class MessageBase(SQLModel):
    content: str = Field(sa_column=Column(Text))
    role: str = Field(
        max_length=50, default="user"
    )  # Can be "user", "assistant", "system", etc.
    is_interrupt: bool = Field(default=False)
    interrupt_message: str | None = Field(default=None)
    action_type: MessageActionType = Field(default=MessageActionType.NONE)


class Message(MessageBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    conversation_id: uuid.UUID = Field(
        foreign_key="conversation.id", ondelete="CASCADE"
    )
    conversation: Conversation = Relationship(back_populates="messages")
    thoughts: list["MessageAgentThought"] = Relationship(
        back_populates="message",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    checkpoint: Optional["MessageCheckpoint"] = Relationship(
        back_populates="message",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    token_usage: "TokenUsage" = Relationship(back_populates="message")
    display_components: list["MessageDisplayComponent"] = Relationship(
        back_populates="message",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    feedback: Optional["MessageFeedback"] = Relationship(
        back_populates="message",
        sa_relationship_kwargs={"cascade": "all, delete-orphan", "uselist": False},
    )
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    message_metadata: dict = Field(default={}, sa_column=Column(JSON))
    is_deleted: bool = Field(default=False)


class MessageDisplayComponent(SQLModel, table=True):
    """
    Model for storing display components (tables and charts) associated with messages
    """

    id: UUID = Field(default_factory=uuid4, primary_key=True)
    message_id: UUID = Field(foreign_key="message.id", ondelete="CASCADE")
    type: MessageDisplayComponentType
    chart_type: ChartType | None = Field(
        default=None, description="Specific type of chart when type is CHART"
    )
    title: str | None = Field(default=None, max_length=255)
    description: str | None = Field(default=None, sa_column=Column(Text))
    data: dict = Field(default={}, sa_column=Column(JSON))
    config: dict = Field(default={}, sa_column=Column(JSON))
    position: int = Field(default=0)  # Order of appearance in the message
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

    # Relationship
    message: "Message" = Relationship(back_populates="display_components")

    class Config:
        from_attributes = True


class MessageDisplayComponentPublic(SQLModel):
    """
    Public schema for message display components
    """

    id: UUID
    type: MessageDisplayComponentType
    chart_type: ChartType | None
    title: str | None
    description: str | None
    data: dict
    config: dict
    position: int
    created_at: datetime

    class Config:
        from_attributes = True


class MessagePublic(SQLModel):
    content: str
    resume: bool
    approve: bool
    restore: bool | None = None
    message_id: uuid.UUID | None = None  # message id to restore from
    action_type: MessageActionType | None = None
    display_components: list[MessageDisplayComponentPublic] | None = None


class MessageHistoryPublic(SQLModel):
    limit: int
    has_more: bool
    data: list[dict] = Field(
        default=[],
        description="list of messages with agent thoughts. Each message contains: id, message_id, position, thought, tool, tool_input, created_at, observation",
    )


### MessageCheckpoint models ###


class MessageCheckpoint(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    message_id: uuid.UUID = Field(foreign_key="message.id", ondelete="CASCADE")
    message: Message = Relationship(back_populates="checkpoint")
    start_checkpoint_id: str | None = Field(default=None)
    end_checkpoint_id: str | None = Field(default=None)


### MessageAgentThought models ###


class MessageAgentThoughtBase(SQLModel):
    position: int
    tool: str = Field(max_length=255)
    tool_input: dict = Field(default={}, sa_column=Column(JSON))
    observation: str = Field(sa_column=Column(Text))
    answer: str = Field(sa_column=Column(Text))


class MessageAgentThought(MessageAgentThoughtBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    message_id: uuid.UUID = Field(foreign_key="message.id", ondelete="CASCADE")
    message: Message = Relationship(back_populates="thoughts")


# Streaming Response


class StreamResponse(BaseModel):
    type: str
    content: str | None = None
    message_id: uuid.UUID | None = None


class ConversationCreateRequest(BaseModel):
    agent_id: UUID4
    model_provider: str = "bedrock"  # Default value
    resource_id: UUID4 | None = None
    instructions: str | None = None  # Optional custom instructions from UI


class MetricRead(MetricPublic):
    class Config:
        arbitrary_types_allowed = True
        from_attributes = True


class ResourceRead(ResourceBase):
    tags: dict[str, str]
    configurations: dict[str, any]
    recommendations: list[RecommendationPublic]
    metrics: dict[str, list[MetricRead]] | None = None
    type: str
    region: str

    class Config:
        arbitrary_types_allowed = True
        from_attributes = True


# TokenUsage Tracking


class TokenUsage(SQLModel, table=True):
    """
    Tracks token usage for individual messages in conversations.

    Attributes:
        id: Unique identifier for the token usage record
        message_id: Reference to the associated message
        input_tokens: Number of tokens in the input message
        output_tokens: Number of tokens in the output message
        model_id: Identifier for the AI model used
        model_provider: Provider of the AI model used
        created_at: Timestamp of record creation
        updated_at: Timestamp of last update
    """

    __tablename__ = "token_usage"

    id: UUID = Field(
        default_factory=uuid4,
        primary_key=True,
        title="ID",
        description="Unique identifier for the token usage record",
    )

    message_id: UUID = Field(
        foreign_key="message.id",
        index=True,
        title="Message ID",
        description="Reference to the associated message",
    )

    message: "Message" = Relationship(
        back_populates="token_usage", sa_relationship_kwargs={"lazy": "joined"}
    )

    input_tokens: int = Field(
        ge=0, title="Input Tokens", description="Number of tokens in the input message"
    )

    output_tokens: int = Field(
        ge=0,
        title="Output Tokens",
        description="Number of tokens in the output message",
    )

    model_provider: str = Field(
        min_length=1,
        title="Model Provider",
        description="Provider of the AI model used",
    )

    model_id: str = Field(
        min_length=1, title="Model ID", description="Identifier for the AI model used"
    )

    workspace_id: UUID = Field(
        foreign_key="workspace.id", index=True, title="Workspace ID", ondelete="CASCADE"
    )

    created_at: datetime = Field(
        sa_column=Column(DateTime(timezone=True), server_default=func.now()),
        title="Created At",
        description="Timestamp of record creation",
    )

    updated_at: datetime = Field(
        sa_column=Column(
            DateTime(timezone=True), server_default=func.now(), onupdate=func.now()
        ),
        title="Updated At",
        description="Timestamp when feedback was last updated",
    )

    @field_validator("input_tokens", "output_tokens")
    def validate_token_counts(cls, v):
        if v < 0:
            raise ValueError("Token count cannot be negative")
        return v

    def get_total_tokens(self) -> int:
        """Calculate total tokens used."""
        return self.input_tokens + self.output_tokens


class UsageQuota(SQLModel, table=True):
    """
    Manages usage quotas for workspaces.
    """

    __tablename__ = "usage_quota"

    id: UUID = Field(default_factory=uuid.uuid4, primary_key=True, title="ID")

    user_id: UUID = Field(
        foreign_key="user.id", index=True, title="User ID", ondelete="CASCADE"
    )

    user: User = Relationship(back_populates="usage_quota")

    quota_used_messages: int = Field(default=0, ge=0, title="Quota Used Messages")

    quota_used_tokens: int = Field(default=0, ge=0, title="Quota Used Tokens")

    reset_at: datetime = Field(
        sa_column=Column(DateTime(timezone=True), server_default=func.now()),
        title="Reset At",
    )

    created_at: datetime = Field(
        sa_column=Column(DateTime(timezone=True), server_default=func.now()),
        title="Created At",
    )

    updated_at: datetime | None = Field(
        None,
        sa_column=Column(DateTime(timezone=True), onupdate=func.now()),
        title="Updated At",
    )


class SavingSummaryReport(SQLModel):
    potential_savings: float
    potential_savings_percentage_change: float
    save_opportunities: float
    save_opportunities_percentage_change: float
    total_saved: float
    total_saved_percentage_change: float
    active_saving: float
    active_saving_percentage_change: float


class ChartDataPoint(SQLModel):
    date: datetime
    value: float


class ResourceSavingsReport(SQLModel):
    rds_savings: list[ChartDataPoint]
    ec2_savings: list[ChartDataPoint]
    total_rds_savings: float
    total_ec2_savings: float


class TopSavingsReport(SQLModel):
    data: list[RecommendationPublic]


class ServiceSavingsData(SQLModel):
    service: str
    savings: float
    percentage: float


class ServiceSavingsReport(SQLModel):
    data: list[ServiceSavingsData]
    total_savings: float


class ResourceType(str, Enum):
    # Compute Services
    EC2 = "EC2"
    LAMBDA = "LAMBDA"
    ECS = "ECS"
    EKS = "EKS"
    BATCH = "BATCH"
    EC2_AUTO_SCALING = "EC2_AUTO_SCALING"
    ELASTIC_BEANSTALK = "ELASTIC_BEANSTALK"
    APP_RUNNER = "APP_RUNNER"

    # Database Services
    RDS = "RDS"
    DYNAMODB = "DYNAMODB"
    ELASTICACHE = "ELASTICACHE"
    NEPTUNE = "NEPTUNE"
    DOCUMENTDB = "DOCUMENTDB"
    OPENSEARCH = "OPENSEARCH"
    REDSHIFT = "REDSHIFT"

    # Storage Services
    S3 = "S3"
    EBS = "EBS"
    EFS = "EFS"
    BACKUP = "BACKUP"

    # Networking & Content Delivery
    VPC = "VPC"
    ELB = "ELB"

    # Management & Governance
    CLOUDFORMATION = "CLOUDFORMATION"
    CLOUDWATCH = "CLOUDWATCH"
    SQS = "SQS"
    SNS = "SNS"


# Task Management
class TaskPriority(int, Enum):
    """
    Enumeration of task priority levels.

    Attributes:
        LOW (0): Regular priority, no urgency
        MEDIUM (1): Moderate priority, should be done soon
        HIGH (2): High priority, urgent attention needed
        CRITICAL (3): Critical priority, requires immediate attention
    """

    LOW = 0
    MEDIUM = 1
    HIGH = 2
    CRITICAL = 3


class TaskScheduledStatus(str, Enum):
    """
    Enumeration of scheduled statuses for task.
    """

    PENDING = "pending"
    SCHEDULED = "scheduled"


class TaskExecutionStatus(str, Enum):
    """
    Enumeration of execution statuses for task.

    Attributes:
        RUNNING: Currently executing
        SUCCEEDED: Successfully completed
        FAILED: Execution failed
        CANCELLED: Execution cancelled
    """

    RUNNING = "running"
    SUCCEEDED = "succeeded"
    FAILED = "failed"
    CANCELLED = "cancelled"


class AuditMixin(SQLModel):
    """
    Mixin class providing audit fields for tracking entity changes.

    Attributes:
        created_at: Timestamp when the record was created (UTC)
        updated_at: Timestamp when the record was last updated (UTC)
        created_by: ID of the user who created the record
        updated_by: ID of the user who last updated the record
        is_deleted: Soft delete flag
        version: Record version for optimistic locking
    """

    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        nullable=False,
        index=True,
        description="Timestamp when the record was created",
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        nullable=False,
        index=True,
        description="Timestamp when the record was last updated",
    )
    created_by: uuid.UUID = Field(
        foreign_key="user.id",
        nullable=False,
        description="User ID who created this record",
    )
    updated_by: uuid.UUID = Field(
        foreign_key="user.id",
        nullable=False,
        description="User ID who last updated this record",
    )
    is_deleted: bool = Field(default=False, index=True, description="Soft delete flag")
    version: int = Field(
        default=1, nullable=False, description="Record version for optimistic locking"
    )


class TaskImpactEnum(str, Enum):
    """
    Enumeration of possible task impacts.
    """

    LOW = 0
    MEDIUM = 1
    HIGH = 2
    CRITICAL = 3


class TaskCategoryEnum(str, Enum):
    """
    Enumeration of possible task categories.
    """

    COST_OPTIMIZE = "COST_OPTIMIZE"
    OPERATIONAL = "OPERATIONAL"
    SCALABILITY = "SCALABILITY"
    SECURITY = "SECURITY"
    OPERATIONAL_EFFICIENCY = "OPERATIONAL_EFFICIENCY"
    OTHER = "OTHER"  # Default category for uncategorized tasks


class TaskCouldEnum(str, Enum):
    AWS = "AWS"
    AZURE = "AZURE"
    GCP = "GCP"
    ALL = "ALL"


class TaskServiceEnum(str, Enum):
    """
    Enumeration of possible task services.
    """

    ALL = "ALL"
    OTHER = "OTHER"  # Default service for uncategorized services
    COMPUTE = "COMPUTE"
    STORAGE = "STORAGE"
    SERVERLESS = "SERVERLESS"
    DATABASE = "DATABASE"
    NETWORK = "NETWORK"
    MESSAGING = "MESSAGING"
    MANAGEMENT = "MANAGEMENT"
    BILLING = "BILLING"
    CROSS_SERVICE = "CROSS_SERVICE"
    MONITORING = "MONITORING"
    STREAMING = "STREAMING"
    SECURITY = "SECURITY"


class TaskBase(SQLModel):
    """
    Base class for all task types with common fields and validations.

    Attributes:
        title: Task title (1-255 characters)
        description: Optional detailed description
        priority: Task priority level
        enable: Whether the task is enabled
        category: Task category
        service: Task service
        service_name: Task service name
        cloud: Task cloud provider
    """

    title: str = Field(
        max_length=MAX_TITLE_LENGTH,
        min_length=1,
        index=True,
        nullable=False,
        description="Task title (required, 1-255 characters)",
    )
    description: str | None = Field(
        default=None,
        max_length=MAX_DESCRIPTION_LENGTH,
        description="Detailed task description (optional)",
    )
    priority: TaskPriority = Field(
        default=TaskPriority.LOW,
        nullable=False,
        index=True,
        description="Task priority level",
    )
    enable: bool = Field(
        default=True,
        nullable=False,
        index=True,
        description="Whether the task is enabled",
    )
    category: TaskCategoryEnum = Field(
        default=TaskCategoryEnum.OTHER,
        index=True,
        description="Task category",
        nullable=True,
    )
    service: TaskServiceEnum = Field(
        default=TaskServiceEnum.OTHER,
        index=True,
        description="AWS service category",
        nullable=True,
    )
    service_name: str = Field(
        default="", index=True, description="Service name", nullable=True
    )
    cloud: TaskCouldEnum = Field(
        default=TaskCouldEnum.AWS,
        index=True,
        description="Cloud provider",
        nullable=True,
    )

    @field_validator("title")
    def validate_title(cls, v):
        """Validate task title."""
        v = v.strip()
        if not v:
            raise ValueError("Title cannot be empty or just whitespace")
        if len(v) > MAX_TITLE_LENGTH:
            raise ValueError(f"Title cannot exceed {MAX_TITLE_LENGTH} characters")
        return v

    @field_validator("description")
    def validate_description(cls, v):
        """Validate task description."""
        v = v.strip()
        if len(v) > MAX_DESCRIPTION_LENGTH:
            raise ValueError(
                f"Description cannot exceed {MAX_DESCRIPTION_LENGTH} characters"
            )
        return v


class TaskHistory(SQLModel, table=True):
    """
    Execution history of a task conversation.
    """

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True, index=True)

    # Foreign key to Task
    task_id: uuid.UUID = Field(foreign_key="task.id", nullable=False, index=True)
    task: "Task" = Relationship(
        back_populates="task_history", sa_relationship_kwargs={"lazy": "selectin"}
    )

    # Conversation identifier
    conversation_id: uuid.UUID = Field(
        foreign_key="conversation.id",
        nullable=False,
        index=True,
    )

    # Task status
    status: TaskExecutionStatus = Field(
        nullable=False,
        index=True,
        description="Current task status",
    )

    # Error message
    error: str | None = Field(
        default=None,
        description="Error message if the task failed",
    )

    # Celery task ID
    celery_task_id: uuid.UUID | None = Field(
        default=None,
        description="Celery task ID associated with the task",
    )

    # Audit
    start_time: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        nullable=False,
        index=True,
        description="Timestamp when the task history was started",
    )
    end_time: datetime | None = Field(
        default=None,
        nullable=True,
        index=True,
        description="Timestamp when the task history was ended",
    )
    run_time: int | None = Field(
        default=None,
        nullable=True,
        index=True,
        description="Time taken to run the task",
    )


class Task(TaskBase, AuditMixin, table=True):
    """
    Unified task model supporting both manual and automated execution modes.
    """

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True, index=True)

    # Last run status
    execution_status: TaskExecutionStatus = Field(
        default=None,
        nullable=True,
        index=True,
        description="Latest execution status of the task",
    )
    error: str | None = Field(
        default=None,
        description="Error message if the latest execution failed",
    )
    last_run: datetime | None = Field(
        default=None,
        index=True,
        description="When this task was last executed/attempted",
    )
    # Celery task ID
    celery_task_id: uuid.UUID | None = Field(
        default=None,
        description="Celery task ID associated with the latest execution task",
    )

    # Scheduled status using for celery scheduler
    scheduled_status: TaskScheduledStatus = Field(
        default=TaskScheduledStatus.PENDING,
        nullable=False,
        index=True,
        description="Current scheduled status of the task",
    )

    # Next run
    next_run: datetime | None = Field(
        default=None,
        index=True,
        description="When this task is next scheduled to run",
        nullable=False,
    )

    # Task history
    task_history: list["TaskHistory"] = Relationship(
        back_populates="task",
        sa_relationship_kwargs={"cascade": "all, delete-orphan", "lazy": "selectin"},
    )

    # Scheduling fields
    schedule: str | None = Field(
        default=None, description="Cron expression for automated task scheduling"
    )

    # Execution fields
    agent_config: dict | None = Field(
        default={},
        sa_column=Column(JSON),
        description="Configuration for automated task execution",
    )

    # Relationships
    # Owner
    owner_id: uuid.UUID = Field(
        foreign_key="user.id",
        nullable=False,
        index=True,
        description="ID of the user who owns this task",
    )
    owner: "User" = Relationship(
        back_populates="owned_tasks",
        sa_relationship_kwargs={"lazy": "selectin", "foreign_keys": "[Task.owner_id]"},
    )
    # Workspace
    workspace_id: uuid.UUID = Field(
        foreign_key="workspace.id", nullable=False, index=True, ondelete="CASCADE"
    )
    workspace: "Workspace" = Relationship(
        back_populates="tasks",
        sa_relationship_kwargs={
            "lazy": "selectin",
            "foreign_keys": "[Task.workspace_id]",
        },
    )


class UploadStatus(str, Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"


class UploadBase(SQLModel):
    filename: str
    file_size: int
    file_type: str


class Upload(UploadBase, table=True):
    id: uuid.UUID = Field(
        default_factory=uuid.uuid4,
        primary_key=True,
        index=True,
        nullable=False,
    )
    s3_key: str
    status: UploadStatus = Field(default=UploadStatus.PENDING)
    upload_url: str
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    owner_id: uuid.UUID = Field(foreign_key="user.id")
    workspace_id: uuid.UUID = Field(foreign_key="workspace.id", ondelete="CASCADE")


class ConnectorType(StrEnum):
    BEDROCK_KB = "bedrock_kb"
    OPEN_API = "open_api"


class RunModeEnum(str, Enum):
    AUTONOMOUS = "autonomous"
    AGENT = "agent"


class TaskCategory(str, Enum):
    """
    Enumeration of possible task categories.
    """

    COST_OPTIMIZE = "COST_OPTIMIZE"
    OPERATIONAL = "OPERATIONAL"
    SCALABILITY = "SCALABILITY"
    SECURITY = "SECURITY"
    OTHER = "OTHER"  # Default category for uncategorized tasks


class TaskService(str, Enum):
    """
    Enumeration of possible task services.
    """

    COMPUTE = "COMPUTE"
    STORAGE = "STORAGE"
    SERVERLESS = "SERVERLESS"
    DATABASE = "DATABASE"
    NETWORK = "NETWORK"
    OTHER = "OTHER"  # Default service for uncategorized services


class TaskTemplate(SQLModel, table=True):
    id: UUID = Field(default_factory=uuid4, primary_key=True)
    workspace_id: UUID | None = Field(
        default=None,
        foreign_key="workspace.id",
        index=True,
        nullable=True,  # Null for default templates
        ondelete="CASCADE",
    )
    task: str = Field(index=True, description="Task name starting with action verb")
    category: TaskCategoryEnum = Field(
        default=TaskCategoryEnum.OTHER, index=True, description="Task category"
    )
    service: TaskServiceEnum = Field(
        default=TaskServiceEnum.OTHER, index=True, description="AWS service category"
    )
    service_name: str = Field(default="", index=True, description="Service name")
    cloud: TaskCouldEnum = Field(
        default=TaskCouldEnum.AWS, index=True, description="Cloud provider"
    )
    run_mode: RunModeEnum = Field(
        default=RunModeEnum.AUTONOMOUS,
        description="Either 'autonomous' or 'agent'",
        nullable=True,
    )
    schedule: str | None = Field(
        default=None, description="Cron syntax for autonomous tasks"
    )
    context: str = Field(description="Detailed task description")
    priority: TaskPriority = Field(
        default=TaskPriority.LOW, description="Priority of the task"
    )
    impact: TaskImpactEnum = Field(
        default=TaskImpactEnum.LOW, description="Impact of the task"
    )
    is_default: bool = Field(
        default=False, description="Whether this is a system default template"
    )
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime | None = None


class ModuleSetting(SQLModel, table=True):
    key: str = Field(primary_key=True, index=True)
    value: dict = Field(default={}, sa_column=Column(JSON))
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime | None = Field(default=None)


class AlertSeverity(str, Enum):
    CRITICAL = "CRITICAL"
    HIGH = "HIGH"
    MEDIUM = "MEDIUM"
    LOW = "LOW"
    INFO = "INFO"


class AlertStatus(str, Enum):
    OPEN = "OPEN"
    ACKNOWLEDGED = "ACKNOWLEDGED"
    RESOLVED = "RESOLVED"
    CLOSED = "CLOSED"


class Alert(SQLModel, table=True):
    id: UUID = Field(default_factory=uuid4, primary_key=True)
    workspace_id: UUID | None = Field(
        default=None,
        foreign_key="workspace.id",
        index=True,
        nullable=True,
        ondelete="CASCADE",
    )
    severity: AlertSeverity = Field(...)
    title: str = Field(..., max_length=200)
    description: str = Field(...)
    status: AlertStatus = Field(default=AlertStatus.OPEN)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime | None = None


# Add new model for activation tokens
class UserActivationToken(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    user_id: uuid.UUID = Field(foreign_key="user.id", ondelete="CASCADE")
    token: str = Field(unique=True, index=True)
    expires_at: datetime
    created_at: datetime = Field(default_factory=datetime.now)
    is_used: bool = Field(default=False)

    user: User = Relationship(
        back_populates="activation_tokens", sa_relationship_kwargs={"lazy": "joined"}
    )


class ActivationResponse(SQLModel):
    message: str
    expires_at: datetime


class ActivationResult(SQLModel):
    success: bool
    message: str
    redirect_url: str | None = None
    welcome_message: str | None = None


class ResendActivationRequest(SQLModel):
    email: EmailStr
    captcha_token: str


class NotificationType(str, Enum):
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    INTERRUPT = "interrupt"


class NotificationStatus(str, Enum):
    UNREAD = "unread"
    READ = "read"
    ARCHIVED = "archived"


class NotificationBase(SQLModel):
    title: str = Field(max_length=255)
    message: str = Field(sa_column=Column(Text))
    type: NotificationType = Field(default=NotificationType.INFO)
    status: NotificationStatus = Field(default=NotificationStatus.UNREAD)
    notification_metadata: dict = Field(
        default_factory=dict,
        sa_column=Column(JSON),
        description="Metadata for the notification",
    )
    requires_action: bool = Field(default=False)
    action_url: str | None = Field(default=None, description="URL for direct action")


class Notification(NotificationBase, table=True):
    id: UUID = Field(default_factory=uuid4, primary_key=True)
    user_id: UUID = Field(foreign_key="user.id", index=True)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    read_at: datetime | None = None
    expires_at: datetime | None = None
    message_id: UUID | None = Field(default=None, description="Message ID")
    # Relationships
    user: User = Relationship(back_populates="notifications")

    class Config:
        from_attributes = True


class NotificationCreate(NotificationBase):
    user_id: UUID


class NotificationUpdate(SQLModel):
    title: str | None = None
    message: str | None = None
    type: NotificationType | None = None
    status: NotificationStatus | None = None
    notification_metadata: dict | None = None
    requires_action: bool | None = None
    expires_at: datetime | None = None


class NotificationResponse(NotificationBase):
    id: UUID
    user_id: UUID
    created_at: datetime
    updated_at: datetime
    read_at: datetime | None = None
    expires_at: datetime | None = None


class NotificationList(SQLModel):
    """Response model for paginated notifications list.

    Attributes:
        data: List of notification items
        count: Total number of items available (before pagination)
    """

    data: list[NotificationResponse]
    count: int  # Changed from total to count for consistency


class MCPServerTransport(str, Enum):
    """Transport types for MCP servers"""

    STDIO = "stdio"
    SSE = "sse"


class BaseMCPServer(BaseModel):
    """Base model for MCP servers"""

    transport: MCPServerTransport
    is_active: bool = Field(default=True, description="Whether the server is active")
    is_builtin: bool = Field(
        default=False, description="Whether the server is built-in and cannot be edited"
    )
    tools_permissions: list[str] = Field(
        default=[], description="Permissions for the tools of the server"
    )


class MCPStdioServer(BaseMCPServer):
    """Configuration for an MCP server using stdio transport"""

    command: str
    args: list[str]
    env: dict[str, str] | None = None
    encoding: str = "utf-8"
    encoding_error_handler: Literal["strict", "ignore", "replace"] = "strict"


class MCPSseServer(BaseMCPServer):
    """Configuration for an MCP server using SSE transport"""

    url: str
    headers: dict[str, str] = Field(default_factory=dict)
    timeout: float = 5.0
    sse_read_timeout: float = 30.0


MCPServerConfig = MCPStdioServer | MCPSseServer


class MCPConfigSchema(BaseModel):
    """Schema for MCP configuration"""

    mcpServers: dict[str, MCPServerConfig] = Field(default_factory=dict)


class MCPConfig(SQLModel, table=True):
    """MCP (Model Context Protocol) configuration settings for a workspace"""

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    workspace_id: uuid.UUID = Field(
        foreign_key="workspace.id",
        unique=True,
        ondelete="CASCADE",
    )
    config: dict = Field(
        default={}, sa_column=Column(JSON), description="MCP server configuration"
    )
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

    # Relationship
    workspace: "Workspace" = Relationship(back_populates="mcp_config")

    def get_server_names(self) -> list[str]:
        """Get list of configured server names"""
        return list(self.config.get("mcpServers", {}).keys())

    def get_server_by_name(self, name: str) -> MCPServerConfig | None:
        """Get server by name"""
        return self.config.get("mcpServers", {}).get(name)


# Response models for API
class MCPServerInfo(BaseModel):
    """Basic information about an MCP server"""

    name: str
    type: MCPServerTransport
    tool_list: list[str]
    resource_list: list[str]
    connected: bool = False
    connection_error: str | None = None
    is_active: bool = True
    is_builtin: bool = Field(
        default=False, description="Whether the server is built-in and cannot be edited"
    )
    tools_permissions: list[str] = Field(
        default=[], description="Permissions for the tools of the server"
    )


class MCPStdioServerResponse(MCPServerInfo):
    """Response model for stdio transport server"""

    command: str
    args: list[str]


class MCPSseServerResponse(MCPServerInfo):
    """Response model for SSE transport server"""

    url: str
    headers: dict[str, str] = Field(default_factory=dict)


class MCPServerListResponse(BaseModel):
    """Response model for listing all MCP servers"""

    workspace_id: uuid.UUID
    servers: list[MCPSseServerResponse | MCPStdioServerResponse]


# Knowledge Base Models
class KBAccessLevel(str, Enum):
    PRIVATE = "private"  # Owner only
    SHARED = "shared"  # Specific users/teams


class KBUsageMode(str, Enum):
    MANUAL = "manual"
    AGENT_REQUESTED = "agent_requested"
    ALWAYS = "always"


class KBBase(SQLModel):
    title: str = Field(min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    access_level: KBAccessLevel = Field(default=KBAccessLevel.PRIVATE)
    usage_mode: KBUsageMode = Field(default=KBUsageMode.MANUAL)
    tags: list[str] = Field(default=[])


class KB(KBBase, table=True):
    __tablename__ = "knowledge_bases"

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)

    # Workspace relationship: Which workspace this KB belongs to
    workspace_id: uuid.UUID | None = Field(
        foreign_key="workspace.id", ondelete="CASCADE"
    )
    workspace: Workspace = Relationship(back_populates="knowledge_bases")

    # Owner relationship: Who owns this KB
    owner_id: uuid.UUID | None = Field(foreign_key="user.id", ondelete="CASCADE")
    owner: User = Relationship(back_populates="knowledge_bases")

    # Access control: Who can view this KB
    allowed_users: list[UUID] = Field(
        default=[], sa_column=Column(PostgreSQLArray(sa_UUID))
    )

    # Audit fields
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    is_deleted: bool = Field(default=False, index=True)

    # Specify DB column types here using sa_column
    tags: list[str] = Field(default=[], sa_column=Column(PostgreSQLArray(String)))

    # Documents
    documents: list["DocumentKB"] = Relationship(back_populates="kb")


class KBCreate(KBBase):
    allowed_users: list[UUID] = Field(default=[])


class KBRead(KBBase):
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    is_deleted: bool
    access_level: KBAccessLevel
    usage_mode: KBUsageMode
    allowed_users: list[UUID] = Field(default_factory=list)
    owner_id: uuid.UUID


class KBUpdate(SQLModel):
    title: str | None = Field(default=None, max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    access_level: KBAccessLevel | None = Field(default=None)
    tags: list[str] | None = Field(default=None)
    allowed_users: list[UUID] | None = Field(default=None)
    usage_mode: KBUsageMode | None = Field(default=None)


class KBsRead(SQLModel):
    data: list[KBRead]
    count: int


class AvailableUser(SQLModel):
    id: uuid.UUID
    email: str
    full_name: str


class AvailableUsersCurrentWorkspace(SQLModel):
    data: list[AvailableUser]
    count: int


# ------------------------- Document in KB -------------------------


class AsyncTaskStatus(str, Enum):
    PENDING = "PENDING"
    IN_PROGRESS = "PROGRESS"
    COMPLETED = "SUCCESS"
    FAILED = "FAILURE"


class DocumentType(str, Enum):
    URL = "url"
    FILE = "file"


class DocumentKBBase(SQLModel):
    name: str = Field(min_length=0, max_length=255)
    type: DocumentType
    url: str | None = None
    deep_crawl: bool = Field(default=False)
    file_name: str | None = None
    file_type: str | None = None
    object_name: str | None = None
    embed_status: AsyncTaskStatus = Field(default=AsyncTaskStatus.PENDING)


class DocumentKBCreate(DocumentKBBase):
    kb_id: uuid.UUID
    parent_id: uuid.UUID | None = Field(default=None)


class DocumentKBRead(DocumentKBBase):
    id: uuid.UUID
    kb_id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    is_deleted: bool
    parent_id: uuid.UUID | None = Field(default=None)
    children: list["DocumentKBRead"] = Field(default=[])


class DocumentKB(DocumentKBBase, table=True):
    __tablename__ = "document_kbs"

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)

    parent_id: uuid.UUID | None = Field(default=None, foreign_key="document_kbs.id")
    parent: "DocumentKB" = Relationship(
        back_populates="children",
        sa_relationship_kwargs={"remote_side": "DocumentKB.id"},
    )
    children: list["DocumentKB"] = Relationship(back_populates="parent")

    kb_id: uuid.UUID = Field(foreign_key="knowledge_bases.id", ondelete="CASCADE")
    kb: KB = Relationship(back_populates="documents")

    created_at: datetime = Field(default_factory=lambda: datetime.now(UTC))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(UTC))
    is_deleted: bool = Field(default=False, index=True)


class DocumentsKBRead(SQLModel):
    data: list[DocumentKBRead]
    count: int


# ------------------------- Agent Context -------------------------
class AgentContextBase(SQLModel):
    title: str = Field(min_length=0, max_length=255)
    context: str = Field(min_length=0, max_length=5000)
    is_active: bool = Field(default=True)


class AgentContext(AgentContextBase, table=True):
    __tablename__ = "agent_contexts"

    # ID fields
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    agent_id: uuid.UUID = Field(foreign_key="agent.id", ondelete="CASCADE")
    agent: Agent = Relationship(back_populates="agent_contexts")

    # Audit fields
    created_at: datetime = Field(default_factory=lambda: datetime.now(UTC))
    is_deleted: bool = Field(default=False, index=False)


class AgentContextCreate(AgentContextBase):
    agent_id: uuid.UUID
    created_at: datetime = Field(default_factory=lambda: datetime.now(UTC))


class AgentContextRead(AgentContextBase):
    id: uuid.UUID
    agent_id: uuid.UUID
    created_at: datetime


class AgentContextUpdate(SQLModel):
    title: str | None = Field(default=None, max_length=255)
    context: str | None = Field(default=None, max_length=5000)
    is_active: bool | None = Field(default=None)


class AgentContextListInput(SQLModel):
    agent_ids: list[uuid.UUID]


class AgentContextListResponse(SQLModel):
    """Response model for paginated agent context list.

    Attributes:
        data: List of agent context items
        count: Total number of items available (before pagination)
    """

    data: list[AgentContextRead]
    count: int  # Changed from total to count for consistency


# TokenUsage Tracking


class FeedbackType(str, Enum):
    """
    Enumeration for feedback types on agent responses.
    """

    GOOD = "good"
    BAD = "bad"


class MessageFeedbackBase(SQLModel):
    """
    Base model for message feedback containing common attributes.
    """

    feedback_type: FeedbackType = Field(description="Type of feedback (good/bad)")
    reason: str | None = Field(
        default=None,
        max_length=1000,
        description="Optional reason for the feedback, required when feedback_type is BAD",
    )
    additional_comments: str | None = Field(
        default=None,
        max_length=2000,
        description="Additional optional comments from the user",
    )


class MessageFeedback(MessageFeedbackBase, table=True):
    """
    Model for storing user feedback on agent messages.

    Attributes:
        id: Unique identifier for the feedback record
        message_id: Reference to the message being rated
        user_id: Reference to the user who provided the feedback
        feedback_type: Whether the feedback is positive or negative
        reason: Optional reason for the feedback (required for bad feedback)
        additional_comments: Optional additional comments
        created_at: Timestamp when feedback was created
        updated_at: Timestamp when feedback was last updated
    """

    __tablename__ = "message_feedback"

    id: UUID = Field(
        default_factory=uuid4,
        primary_key=True,
        title="ID",
        description="Unique identifier for the feedback record",
    )

    message_id: UUID = Field(
        foreign_key="message.id",
        index=True,
        title="Message ID",
        description="Reference to the message being rated",
    )

    user_id: UUID = Field(
        foreign_key="user.id",
        index=True,
        title="User ID",
        description="Reference to the user who provided the feedback",
    )

    created_at: datetime = Field(
        sa_column=Column(DateTime(timezone=True), server_default=func.now()),
        title="Created At",
        description="Timestamp when feedback was created",
    )

    updated_at: datetime = Field(
        sa_column=Column(
            DateTime(timezone=True), server_default=func.now(), onupdate=func.now()
        ),
        title="Updated At",
        description="Timestamp when feedback was last updated",
    )

    # Relationships
    message: "Message" = Relationship(
        back_populates="feedback", sa_relationship_kwargs={"lazy": "joined"}
    )

    # Ensure one feedback per message
    __table_args__ = (UniqueConstraint("message_id", name="uq_message_feedback"),)


class MessageFeedbackCreate(MessageFeedbackBase):
    """Schema for creating message feedback"""

    message_id: UUID


class MessageFeedbackUpdate(SQLModel):
    """Schema for updating message feedback"""

    feedback_type: FeedbackType | None = None
    reason: str | None = None
    additional_comments: str | None = None


class MessageFeedbackPublic(MessageFeedbackBase):
    """Public schema for message feedback responses"""

    id: UUID
    message_id: UUID
    user_id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class MessageFeedbackListResponse(SQLModel):
    """Response model for paginated message feedback list"""

    data: list[MessageFeedbackPublic]
    count: int


class SeedVersion(SQLModel, table=True):
    """Tracks versions of seed data that have been applied to the database."""

    id: UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    seed_type: str = Field(
        index=True, description="Type of seed data (e.g., 'task_templates', 'settings')"
    )
    version: str = Field(description="Version identifier for the seed data")
    applied_at: datetime = Field(default_factory=datetime.now)
    description: str | None = Field(
        default=None, description="Description of what this seed version contains"
    )
    checksum: str = Field(description="Checksum of the seed data to detect changes")
    is_active: bool = Field(
        default=True, description="Whether this seed version is active"
    )
