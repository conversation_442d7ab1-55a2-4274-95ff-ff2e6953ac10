import logging
from datetime import datetime
from uuid import UUID

from sqlmodel import and_, func, select, text
from sqlmodel.ext.asyncio.session import AsyncSession

from app.core.config import settings
from app.core.exceptions import TokenTrackingError, UserQuotaNotFoundError
from app.models import (
    Agent,
    Conversation,
    Message,
    TokenUsage,
    UsageQuota,
    User,
    Workspace,
)
from app.schemas.token_usage import (
    AgentTypeUsage,
    DailyMessageVolume,
    DailyTokenUsage,
    MessageStatistics,
    TokenDistributionCategory,
    TokenUsageResponse,
    UsageStatistics,
)
from app.services.payment_service import PaymentClient, PaymentService

logger = logging.getLogger(__name__)


class TokenUsageService:
    """
    Service for managing token usage tracking and quota management.
    """

    def __init__(self, db_session: AsyncSession):
        self.db = db_session
        self.payment_client = PaymentClient()
        self.payment_service = PaymentService(self.payment_client)

    async def update_or_create_token_usage(
        self,
        message_id: UUID,
        input_tokens: int,
        output_tokens: int,
        model_id: str,
        model_provider: str,
        workspace_id: UUID,
    ) -> TokenUsageResponse:
        """
        Update an existing token usage record by adding token counts, or create a new one if it doesn't exist.

        Args:
            message_id: ID of the message
            input_tokens: Number of input tokens to add
            output_tokens: Number of output tokens to add
            model_id: ID of the model used,
            model_provider: Provider of the AI model used,
            workspace_id: ID of the workspace

        Returns:
            TokenUsageResponse with updated or created record details

        Raises:
            QuotaExceededException: If workspace quota would be exceeded
            TokenTrackingError: If token tracking fails
        """
        try:
            # Check if a token usage record already exists for this message
            stmt = select(TokenUsage).where(TokenUsage.message_id == message_id)
            result = await self.db.execute(stmt)
            existing_usage = result.scalars().first()

            # Get the workspace owner's ID
            workspace_stmt = select(Workspace).where(Workspace.id == workspace_id)
            workspace_result = await self.db.execute(workspace_stmt)
            workspace = workspace_result.scalars().first()

            if not workspace:
                raise TokenTrackingError(f"Workspace {workspace_id} not found")

            owner_id = workspace.owner_id

            # Get or create the user's quota
            user_quota = await self._get_or_create_user_quota(owner_id)

            # Calculate total tokens for this operation
            total_tokens = input_tokens + output_tokens

            if existing_usage:
                # Update existing record by adding token counts
                logger.info(
                    f"Found existing token usage for message {message_id}. "
                    f"Current: input={existing_usage.input_tokens}, output={existing_usage.output_tokens}. "
                    f"Adding: input={input_tokens}, output={output_tokens}"
                )

                # Update the token counts by addition
                existing_usage.input_tokens += input_tokens
                existing_usage.output_tokens += output_tokens

                # Raise an error if the model_id has changed
                if existing_usage.model_id != model_id:
                    raise TokenTrackingError(
                        f"Model ID has changed for message {message_id}. "
                        f"Expected: {model_id}, Actual: {existing_usage.model_id}"
                    )

                token_usage = existing_usage
            else:
                # Create new token usage record
                token_usage = TokenUsage(
                    message_id=message_id,
                    input_tokens=input_tokens,
                    output_tokens=output_tokens,
                    model_id=model_id,
                    model_provider=model_provider,
                    workspace_id=workspace_id,
                )
                self.db.add(token_usage)

            # Update user quota
            user_quota.quota_used_tokens += total_tokens
            user_quota.quota_used_messages += (
                0 if existing_usage else 1
            )  # Increment message count only for new messages

            await self.db.commit()
            await self.db.refresh(token_usage)

            logger.info(
                f"Successfully {'updated' if existing_usage else 'created'} token usage for message {message_id}: "
                f"input_tokens={token_usage.input_tokens}, "
                f"output_tokens={token_usage.output_tokens}, "
                f"model={model_id}, "
                f"workspace={workspace_id}, "
                f"user={owner_id}"
            )

            return TokenUsageResponse.model_validate(token_usage)

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to update or create token usage: {str(e)}")
            raise TokenTrackingError(f"Failed to track token usage: {str(e)}")

    async def _create_usage_quota(self, user_id: UUID) -> UsageQuota:
        """
        Create usage quota for a user.
        """
        try:
            stmt = select(UsageQuota).where(UsageQuota.user_id == user_id)
            result = await self.db.execute(stmt)
            existing_quota = result.scalars().first()

            if existing_quota:
                return existing_quota

            new_quota = UsageQuota(
                user_id=user_id, quota_used_messages=0, quota_used_tokens=0
            )

            self.db.add(new_quota)
            await self.db.commit()
            await self.db.refresh(new_quota)

            logger.info(f"Created quota for user {user_id}")
            return new_quota

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to initialize quota for user {user_id}: {str(e)}")
            raise TokenTrackingError(f"Failed to initialize user quota: {str(e)}")

    async def _update_usage_quota(
        self, user_id: UUID, messages_count: int = 0, tokens_count: int = 0
    ) -> None:
        """
        Update the user's quota usage.

        Args:
            user_id: ID of the user
            messages_count: Number of messages to add to usage
            tokens_count: Number of tokens to add to usage
        """
        user = await self.db.get(User, user_id)
        if not user:
            raise TokenTrackingError(f"User {user_id} not found")

        stmt = select(UsageQuota).where(UsageQuota.user_id == user_id)
        result = await self.db.exec(stmt)
        quota = result.first()

        if quota:
            quota.quota_used_messages += messages_count
            quota.quota_used_tokens += tokens_count
        else:
            raise TokenTrackingError(f"No quota record found for user {user_id}")

    async def _get_usage_quota(self, user_id: UUID) -> UsageQuota:
        """
        Get user quota information.

        Args:
            user_id: ID of the user

        Returns:
            UsageQuota object for the user

        Raises:
            TokenTrackingError: If quota record not found
        """
        stmt = select(UsageQuota).where(UsageQuota.user_id == user_id)
        result = await self.db.exec(stmt)
        quota = result.first()

        if not quota:
            raise TokenTrackingError(f"No quota record found for user {user_id}")

        return quota

    async def _reset_user_quota(self, user_id: UUID) -> UsageQuota:
        """
        Reset usage quota for a user.

        Args:
            user_id: ID of the user

        Returns:
            Updated UsageQuota object
        """
        quota = await self._get_usage_quota(user_id)
        quota.quota_used_messages = 0
        quota.quota_used_tokens = 0
        quota.reset_at = datetime.now()

        await self.db.commit()
        await self.db.refresh(quota)

        return quota

    async def _check_out_of_quota(self, user_id: UUID) -> bool:
        """
        Check if the user has exceeded their usage quota.

        Args:
            user_id: ID of the user

        Returns:
            True if the user is out of quota, False otherwise
        """
        try:
            quota_info = await self.get_user_quota_info(user_id)
            quota_used = quota_info["quota_used"]
            quota_limit = quota_info["quota_limit"]

            return quota_used >= quota_limit

        except Exception as e:
            logger.error(f"Failed to check quota for user {user_id}: {str(e)}")
            # Default to not out of quota if there's an error
            return False

    async def get_usage_statistics(
        self,
        user_id: UUID,
        start_date: datetime | None = None,
        end_date: datetime | None = None,
    ) -> UsageStatistics:
        """
        Get token usage statistics for a workspace.

        Args:
            user_id: ID of the user
            start_date: Optional start date for filtering
            end_date: Optional end date for filtering

        Returns:
            UsageStatistics including aggregate stats and time series data
        """
        try:
            # Get all workspaces owned by this user
            workspaces_query = (
                select(Workspace.id)
                .where(Workspace.owner_id == user_id)
                .where(Workspace.is_deleted == False)
            )
            workspaces_result = await self.db.execute(workspaces_query)
            user_workspace_ids = [row[0] for row in workspaces_result]

            if not user_workspace_ids:
                logger.warning(f"User {user_id} doesn't own any workspaces")
                raise UserQuotaNotFoundError(
                    f"User {user_id} doesn't own any workspaces"
                )

            # Base query for token usage with joins to get agent type
            base_query = (
                select(TokenUsage, Agent.type.label("agent_type"))
                .join(Message, TokenUsage.message_id == Message.id)
                .join(Conversation, Message.conversation_id == Conversation.id)
                .join(Agent, Conversation.agent_id == Agent.id)
                .where(TokenUsage.workspace_id.in_(user_workspace_ids))
            )

            # Apply date filters if provided
            if start_date:
                base_query = base_query.where(TokenUsage.created_at >= start_date)
            if end_date:
                base_query = base_query.where(TokenUsage.created_at <= end_date)

            # Get all usage records for the period
            result = await self.db.execute(base_query)
            usage_records = result.all()

            # Initialize aggregation dictionaries
            daily_usage = {}
            agent_type_usage = {}
            total_input = 0
            total_output = 0

            # Calculate totals and aggregate by date and agent type
            for record in usage_records:
                usage = record[0]  # TokenUsage object
                agent_type = record[1]  # agent_type from Agent

                # Update totals
                total_input += usage.input_tokens
                total_output += usage.output_tokens

                # Aggregate by date
                date_key = usage.created_at.date()
                if date_key not in daily_usage:
                    daily_usage[date_key] = {"total_tokens": 0}
                daily_usage[date_key]["total_tokens"] += (
                    usage.input_tokens + usage.output_tokens
                )

                # Aggregate by agent type
                if agent_type not in agent_type_usage:
                    agent_type_usage[agent_type] = {
                        "input_tokens": 0,
                        "output_tokens": 0,
                    }
                agent_type_usage[agent_type]["input_tokens"] += usage.input_tokens
                agent_type_usage[agent_type]["output_tokens"] += usage.output_tokens

            # Convert daily usage to time series data points
            daily_token_usage = [
                DailyTokenUsage(
                    date=datetime.combine(date, datetime.min.time()),
                    total_tokens=data["total_tokens"],
                )
                for date, data in sorted(daily_usage.items())
            ]

            # Convert agent type usage to list
            agent_type_stats = [
                AgentTypeUsage(
                    agent_type=agent_type,
                    total_tokens=data["input_tokens"] + data["output_tokens"],
                )
                for agent_type, data in agent_type_usage.items()
            ]

            # Get quota information using the new method
            quota_info = await self.get_user_quota_info(user_id)

            logger.info("Quota limit: %s", quota_info["quota_limit"])
            logger.info("Quota used: %s", quota_info["quota_used"])
            logger.info("Quota remaining: %s", quota_info["quota_remaining"])
            logger.info("Usage percentage: %s", quota_info["usage_percentage"])

            return UsageStatistics(
                input_tokens=total_input,
                output_tokens=total_output,
                total_tokens=total_input + total_output,
                quota_limit=quota_info["quota_limit"],
                quota_used=quota_info["quota_used"],
                quota_remaining=quota_info["quota_remaining"],
                usage_percentage=quota_info["usage_percentage"],
                daily_token_usage=daily_token_usage,
                agent_type_stats=agent_type_stats,
            )

        except Exception as e:
            logger.error(f"Failed to get usage statistics for user {user_id}: {str(e)}")
            raise TokenTrackingError(f"Failed to get usage statistics: {str(e)}")

    async def get_model_usage_breakdown(
        self,
        workspace_id: UUID,
        start_date: datetime | None = None,
        end_date: datetime | None = None,
    ) -> list[dict]:
        """
        Get token usage breakdown by model.

        Args:
            workspace_id: ID of the workspace
            start_date: Optional start date for filtering
            end_date: Optional end date for filtering

        Returns:
            List of dictionaries with model usage stats
        """
        stmt = (
            select(
                TokenUsage.model_id,
                func.sum(TokenUsage.input_tokens).label("total_input"),
                func.sum(TokenUsage.output_tokens).label("total_output"),
                func.count().label("request_count"),
            )
            .join(UsageQuota)
            .where(UsageQuota.workspace_id == workspace_id)
            .group_by(TokenUsage.model_id)
        )

        if start_date:
            stmt = stmt.where(TokenUsage.created_at >= start_date)
        if end_date:
            stmt = stmt.where(TokenUsage.created_at <= end_date)

        result = await self.db.exec(stmt)
        return [
            {
                "model_id": row.model_id,
                "total_input_tokens": row.total_input,
                "total_output_tokens": row.total_output,
                "total_tokens": row.total_input + row.total_output,
                "request_count": row.request_count,
            }
            for row in result.all()
        ]

    async def get_message_statistics(
        self,
        workspace_id: UUID,
        start_date: datetime | None = None,
        end_date: datetime | None = None,
    ) -> MessageStatistics:
        """
        Get message statistics for a workspace within the specified date range.
        """
        logger.info(f"Getting message statistics for workspace {workspace_id}")
        logger.info(f"Date range: {start_date} to {end_date}")
        # Base query for the workspace
        first_messages_subquery = (
            select(Message.id)
            .join(Conversation)
            .join(Agent)
            .where(Agent.workspace_id == workspace_id)
            .distinct(Conversation.id)
            .order_by(Conversation.id, Message.created_at)
        )

        base_query = (
            select(Message)
            .join(Conversation)
            .join(Agent)
            .where(
                and_(
                    Agent.workspace_id == workspace_id,
                    Message.role.notin_(["user", "system"]),
                    Message.id.notin_(first_messages_subquery),
                )
            )
        )

        if start_date is not None:
            base_query = base_query.where(Message.created_at >= start_date)
        if end_date is not None:
            base_query = base_query.where(Message.created_at <= end_date)

        # Get total messages
        total_messages_query = select(func.count()).select_from(base_query.subquery())
        total_messages = await self.db.scalar(total_messages_query) or 0

        # Get average response time by calculating time between user message and assistant response
        response_time_query = select(
            func.avg(func.extract("epoch", Message.updated_at - Message.created_at))
        ).select_from(base_query)
        avg_response_time = await self.db.scalar(response_time_query) or 0.0

        # Get average tokens
        avg_tokens_query = (
            select(
                func.avg(TokenUsage.input_tokens).label("avg_input"),
                func.avg(TokenUsage.output_tokens).label("avg_output"),
            )
            .select_from(base_query)
            .join(TokenUsage)
        )
        avg_tokens_result = await self.db.execute(avg_tokens_query)
        avg_tokens = avg_tokens_result.first()
        avg_input_tokens = avg_tokens.avg_input or 0.0
        avg_output_tokens = avg_tokens.avg_output or 0.0

        # Get daily message volume
        daily_volume_query = (
            select(
                func.date_trunc("day", Message.created_at).label("date"),
                func.count().label("total_messages"),
            )
            .select_from(Message)
            .join(Conversation)
            .join(Agent)
            .where(
                and_(
                    Agent.workspace_id == workspace_id,
                    Message.role.notin_(["system"]),
                    Message.id.notin_(first_messages_subquery),
                )
            )
        )

        if start_date is not None:
            daily_volume_query = daily_volume_query.where(
                Message.created_at >= start_date
            )
        if end_date is not None:
            daily_volume_query = daily_volume_query.where(
                Message.created_at <= end_date
            )

        daily_volume_query = daily_volume_query.group_by(text("date")).order_by(
            text("date")
        )
        daily_volume_result = await self.db.execute(daily_volume_query)
        daily_message_volume = [
            DailyMessageVolume(date=row.date, message_count=row.total_messages)
            for row in daily_volume_result
        ]

        logger.info(f"Daily message volume query returned {len(daily_message_volume)} days of data")
        for volume in daily_message_volume:
            logger.info(f"Date: {volume.date}, Messages: {volume.message_count}")

        # Calculate token distribution
        token_ranges = [
            (0, 3000, "Short (0-3000)"),
            (3000, 10000, "Medium (3001-10000)"),
            (10000, None, "Long (10001+)"),
        ]

        distribution_data = []
        total_count = 0

        for start_tokens, end_tokens, category in token_ranges:
            query = (
                select(func.count())
                .select_from(base_query)
                .join(TokenUsage)
                .where(
                    TokenUsage.input_tokens + TokenUsage.output_tokens >= start_tokens
                )
            )
            if end_tokens:
                query = query.where(
                    TokenUsage.input_tokens + TokenUsage.output_tokens <= end_tokens
                )

            count = await self.db.scalar(query) or 0
            total_count += count
            distribution_data.append((category, count))

        token_distribution = [
            TokenDistributionCategory(
                category=category,
                percentage=round(
                    (count / total_count) * 100 if total_count > 0 else 0, 1
                ),
            )
            for category, count in distribution_data
        ]

        return MessageStatistics(
            total_messages=total_messages,
            average_response_time=avg_response_time,
            average_input_tokens_per_message=avg_input_tokens,
            average_output_tokens_per_message=avg_output_tokens,
            daily_message_volume=daily_message_volume,
            token_distribution_by_message_length=token_distribution,
        )

    async def _get_or_create_user_quota(self, user_id: UUID) -> UsageQuota:
        """
        Get or create usage quota for a user.

        Args:
            user_id: ID of the user

        Returns:
            UsageQuota object for the user
        """
        try:
            stmt = select(UsageQuota).where(UsageQuota.user_id == user_id)
            result = await self.db.execute(stmt)
            existing_quota = result.scalars().first()

            if existing_quota:
                return existing_quota

            new_quota = UsageQuota(
                user_id=user_id, quota_used_messages=0, quota_used_tokens=0
            )

            self.db.add(new_quota)
            await self.db.commit()
            await self.db.refresh(new_quota)

            logger.info(f"Created quota for user {user_id}")
            return new_quota

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to get or create quota for user {user_id}: {str(e)}")
            raise TokenTrackingError(f"Failed to get or create user quota: {str(e)}")

    async def get_user_quota_info(self, user_id: UUID) -> dict:
        """
        Get the user's quota information including used quota and quota limit.

        Args:
            user_id: ID of the user

        Returns:
            Dictionary containing quota_used and quota_limit

        Raises:
            TokenTrackingError: If quota information cannot be retrieved
        """
        try:
            # Get the user's usage quota
            quota = await self._get_or_create_user_quota(user_id)

            # Get the user's quota limit from payment service
            user_quota = await self.payment_service.get_user_quota(user_id)
            quota_limit = (
                user_quota.quota_definition.max_fast_requests_per_month
                if user_quota.quota_definition
                else settings.DEFAULT_USER_QUOTA_LIMIT
            )

            return {
                "quota_used": quota.quota_used_messages,
                "quota_limit": quota_limit,
                "quota_remaining": max(0, quota_limit - quota.quota_used_messages),
                "usage_percentage": round(
                    (quota.quota_used_messages / quota_limit * 100)
                    if quota_limit > 0
                    else 0.0,
                    2,
                ),
            }

        except Exception as e:
            logger.error(
                f"Failed to get quota information for user {user_id}: {str(e)}"
            )
            raise TokenTrackingError(f"Failed to get quota information: {str(e)}")
